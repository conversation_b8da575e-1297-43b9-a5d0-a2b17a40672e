
'use client'

import { Badge } from './badge'
import { useTranslation } from 'react-i18next'
import type { UserRole } from '@/lib/types'

interface RoleBadgeProps {
  role: UserRole
}

const roleColors = {
  zakat_applicant: 'default',
  reception_staff: 'secondary',
  researcher: 'outline',
  banking_expert: 'destructive',
  department_head: 'default',
  admin_manager: 'secondary',
  minister: 'outline',
  system_admin: 'destructive',
} as const

export function RoleBadge({ role }: RoleBadgeProps) {
  const { t } = useTranslation()
  
  return (
    <Badge variant={roleColors[role]}>
      {t(role)}
    </Badge>
  )
}
