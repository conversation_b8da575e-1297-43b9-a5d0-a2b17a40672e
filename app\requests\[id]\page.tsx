
'use client'

import { useSession } from 'next-auth/react'
import { useTranslation } from 'react-i18next'
import { useParams, useRouter } from 'next/navigation'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { StatusBadge } from '@/components/ui/status-badge'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  ArrowLeft, 
  FileText, 
  Download, 
  MessageSquare, 
  CheckCircle, 
  XCircle, 
  RotateCcw, 
  Clock,
  User,
  CreditCard,
  Calendar
} from 'lucide-react'
import { mockAssistanceRequests, getUserById } from '@/lib/mock-data'
import { format } from 'date-fns'
import { ar } from 'date-fns/locale'
import Link from 'next/link'
import { useState } from 'react'
import { toast } from '@/hooks/use-toast'

export default function RequestDetailsPage() {
  const { data: session } = useSession() || {}
  const { t, i18n } = useTranslation()
  const router = useRouter()
  const params = useParams()
  const requestId = params?.id as string

  const [reviewNotes, setReviewNotes] = useState('')
  const [reviewDecision, setReviewDecision] = useState('')

  if (!session?.user) {
    return null
  }

  // Find the request
  const request = mockAssistanceRequests.find(req => req.id === requestId)
  if (!request) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-2">الطلب غير موجود</h2>
            <p className="text-muted-foreground mb-4">لم يتم العثور على الطلب المحدد</p>
            <Button asChild>
              <Link href="/requests">العودة للطلبات</Link>
            </Button>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  const requestUser = getUserById(request.userId)
  const isApplicant = session.user.role === 'zakat_applicant'
  const canReview = !isApplicant && ['reception_staff', 'researcher', 'banking_expert', 'department_head', 'admin_manager', 'minister'].includes(session.user.role)

  const formatDate = (date: Date) => {
    return format(date, 'dd/MM/yyyy HH:mm', {
      locale: i18n.language === 'ar' ? ar : undefined
    })
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(i18n.language === 'ar' ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: 'SAR',
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const handleReviewSubmit = () => {
    if (!reviewDecision || !reviewNotes.trim()) {
      toast({
        title: 'خطأ في النموذج',
        description: 'يرجى اختيار القرار وإدخال الملاحظات',
        variant: 'destructive',
      })
      return
    }

    toast({
      title: 'تم حفظ المراجعة',
      description: `تم ${reviewDecision === 'approve' ? 'الموافقة على' : 'رفض'} الطلب بنجاح`,
    })

    // In a real app, this would update the request status and create workflow entry
    router.push('/tasks')
  }

  const getWorkflowStageLabel = (stage: string) => {
    const labels = {
      reception_review: 'مراجعة الاستقبال',
      researcher_review: 'مراجعة الباحث',
      banking_expert_review: 'مراجعة الخبير المصرفي',
      department_head_review: 'مراجعة رئيس القسم',
      admin_manager_review: 'مراجعة مدير الإدارة',
      minister_review: 'مراجعة الوزير',
      approved: 'موافق عليه',
      rejected: 'مرفوض'
    }
    return labels[stage as keyof typeof labels] || stage
  }

  return (
    <DashboardLayout>
      <div className="space-y-6 max-w-6xl">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/requests">
              <ArrowLeft className="h-4 w-4 mr-2" />
              العودة
            </Link>
          </Button>
          <div className="flex-1">
            <div className="flex items-center gap-3">
              <h1 className="text-3xl font-bold tracking-tight">
                تفاصيل الطلب {requestId}
              </h1>
              <StatusBadge status={request.status} />
            </div>
            <p className="text-muted-foreground">
              {i18n.language === 'ar' ? request.assistanceType.nameAr : request.assistanceType.nameEn}
            </p>
          </div>
          {request.status === 'approved' && (
            <Button>
              <Download className="mr-2 h-4 w-4" />
              تحميل القرار
            </Button>
          )}
        </div>

        <div className="grid gap-6 lg:grid-cols-3">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Request Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  تفاصيل الطلب
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Basic Info */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">نوع المساعدة</p>
                    <p className="font-medium">
                      {i18n.language === 'ar' ? request.assistanceType.nameAr : request.assistanceType.nameEn}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">المبلغ المطلوب</p>
                    <p className="font-medium">{formatCurrency(request.requestedAmount)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">تاريخ التقديم</p>
                    <p className="font-medium">{formatDate(request.submissionDate)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">الأولوية</p>
                    <Badge className={
                      request.priority === 'urgent' ? 'bg-red-100 text-red-800' :
                      request.priority === 'high' ? 'bg-orange-100 text-orange-800' :
                      request.priority === 'medium' ? 'bg-blue-100 text-blue-800' :
                      'bg-gray-100 text-gray-800'
                    }>
                      {request.priority === 'urgent' && 'عاجلة'}
                      {request.priority === 'high' && 'عالية'}
                      {request.priority === 'medium' && 'متوسطة'}
                      {request.priority === 'low' && 'منخفضة'}
                    </Badge>
                  </div>
                </div>

                {/* Description */}
                <div>
                  <h4 className="font-medium mb-2">وصف الحالة</h4>
                  <p className="text-sm text-muted-foreground bg-muted p-4 rounded-lg">
                    {request.description}
                  </p>
                </div>

                {/* Approved Amount (if different) */}
                {request.approvedAmount && request.approvedAmount !== request.requestedAmount && (
                  <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                    <div className="flex items-center gap-2 mb-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <h4 className="font-medium text-green-900">المبلغ المعتمد</h4>
                    </div>
                    <p className="text-2xl font-bold text-green-700">
                      {formatCurrency(request.approvedAmount)}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Documents */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>المستندات المرفقة</span>
                  <Badge variant="secondary">
                    {request.attachedDocuments?.length || 0} مستند
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {!request.attachedDocuments || request.attachedDocuments.length === 0 ? (
                  <p className="text-center text-muted-foreground py-8">
                    لا توجد مستندات مرفقة
                  </p>
                ) : (
                  <div className="space-y-2">
                    {request.attachedDocuments.map((doc, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <FileText className="h-5 w-5 text-muted-foreground" />
                          <div>
                            <p className="font-medium">مستند {index + 1}</p>
                            <p className="text-sm text-muted-foreground">{doc.type}</p>
                          </div>
                        </div>
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Workflow History */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  سجل المراجعة
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {request.workflow.map((step, index) => (
                    <div key={step.id} className="flex items-start gap-4 pb-4 border-b last:border-b-0">
                      <div className="flex-shrink-0 mt-1">
                        {step.decision === 'approve' ? (
                          <CheckCircle className="h-5 w-5 text-green-600" />
                        ) : step.decision === 'reject' ? (
                          <XCircle className="h-5 w-5 text-red-600" />
                        ) : step.decision === 'return' ? (
                          <RotateCcw className="h-5 w-5 text-orange-600" />
                        ) : (
                          <Clock className="h-5 w-5 text-gray-400" />
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-1">
                          <h4 className="font-medium">{getWorkflowStageLabel(step.stage)}</h4>
                          <span className="text-sm text-muted-foreground">
                            {formatDate(step.stageStartDate)}
                          </span>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">
                          المراجع: {step.reviewerName}
                        </p>
                        {step.notes && (
                          <p className="text-sm bg-muted p-2 rounded">
                            {step.notes}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Applicant Info */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  بيانات المتقدم
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <p className="text-sm text-muted-foreground">الاسم</p>
                  <p className="font-medium">{requestUser?.fullName || 'غير متوفر'}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">رقم الهوية</p>
                  <p className="font-medium">{requestUser?.nationalId || 'غير متوفر'}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">الجنسية</p>
                  <p className="font-medium">{requestUser?.nationality || 'غير متوفر'}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">رقم الهاتف</p>
                  <p className="font-medium">{requestUser?.phoneNumber || 'غير متوفر'}</p>
                </div>
              </CardContent>
            </Card>

            {/* Review Actions */}
            {canReview && request.status !== 'approved' && request.status !== 'rejected' && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MessageSquare className="h-5 w-5" />
                    مراجعة الطلب
                  </CardTitle>
                  <CardDescription>
                    أدخل قرارك وملاحظاتك حول الطلب
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="decision">القرار</Label>
                    <Select value={reviewDecision} onValueChange={setReviewDecision}>
                      <SelectTrigger>
                        <SelectValue placeholder="اختر القرار" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="approve">موافقة</SelectItem>
                        <SelectItem value="reject">رفض</SelectItem>
                        <SelectItem value="return">إرجاع للمراجعة</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="notes">الملاحظات</Label>
                    <Textarea
                      id="notes"
                      placeholder="أدخل ملاحظاتك وأسباب القرار..."
                      value={reviewNotes}
                      onChange={(e) => setReviewNotes(e.target.value)}
                      rows={4}
                    />
                  </div>

                  <Button 
                    onClick={handleReviewSubmit}
                    className="w-full"
                    disabled={!reviewDecision || !reviewNotes.trim()}
                  >
                    حفظ المراجعة
                  </Button>
                </CardContent>
              </Card>
            )}

            {/* Quick Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  إحصائيات سريعة
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">أيام المعالجة</span>
                  <span className="font-medium">
                    {Math.ceil((new Date().getTime() - request.submissionDate.getTime()) / (1000 * 60 * 60 * 24))} يوم
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">مراحل المراجعة</span>
                  <span className="font-medium">{request.workflow.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">المستندات</span>
                  <span className="font-medium">{request.attachedDocuments?.length || 0}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
