
'use client'

import { useTranslation } from 'react-i18next'
import { useEffect, useState } from 'react'

export function useSafeTranslation() {
  const { t, i18n } = useTranslation()
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  // Safe translation function that provides fallbacks to prevent hydration mismatch
  const st = (key: string, fallback?: string) => {
    if (!isClient) {
      // During SSR or before hydration, return the fallback or Arabic translation
      const translations: Record<string, string> = {
        'login': 'تسجيل الدخول',
        'register': 'إنشاء حساب', 
        'email': 'البريد الإلكتروني',
        'password': 'كلمة المرور',
        'login_tawtheeq': 'تسجيل الدخول عبر توثيق',
        'login_success': 'تم تسجيل الدخول بنجاح',
        'login_error': 'خطأ في البيانات المدخلة',
        'dashboard': 'لوحة التحكم',
        'profile': 'الملف الشخصي',
        'requests': 'الطلبات',
        'tasks': 'المهام',
        'reports': 'التقارير',
        'settings': 'الإعدادات',
        'logout': 'تسجيل الخروج',
      }
      return fallback || translations[key] || key
    }
    
    return t(key)
  }

  return { t: st, i18n, isClient }
}
