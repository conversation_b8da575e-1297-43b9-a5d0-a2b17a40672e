
'use client'

import { useSession } from 'next-auth/react'
import { useTranslation } from 'react-i18next'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { StatsCard } from '@/components/dashboard/stats-card'
import { RecentRequests } from '@/components/dashboard/recent-requests'
import { 
  FileText, 
  Clock, 
  CheckCircle, 
  XCircle, 
  TrendingUp, 
  Users 
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { mockDashboardStats, mockAssistanceRequests, getRequestsByUserId } from '@/lib/mock-data'
import type { UserRole } from '@/lib/types'

export default function DashboardPage() {
  const { data: session } = useSession() || {}
  const { t } = useTranslation()

  if (!session?.user) {
    return null
  }

  const userRole = session.user.role as UserRole
  const stats = mockDashboardStats[userRole]
  
  // Get user-specific requests
  const userRequests = userRole === 'zakat_applicant' 
    ? getRequestsByUserId(session.user.id)
    : mockAssistanceRequests.slice(0, 5)

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">
            {t('welcome')}, {session.user.name}
          </h1>
          <p className="text-muted-foreground">
            نظرة عامة على حالة {userRole === 'zakat_applicant' ? 'طلباتك' : 'المهام المخصصة لك'}
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatsCard
            title={t('total_requests')}
            value={stats.totalRequests}
            description="إجمالي الطلبات"
            icon={FileText}
          />
          <StatsCard
            title={t('pending_review')}
            value={stats.pendingReview}
            description="في انتظار المراجعة"
            icon={Clock}
          />
          <StatsCard
            title={t('approved_today')}
            value={stats.approvedToday}
            description="موافق عليها اليوم"
            icon={CheckCircle}
          />
          <StatsCard
            title={t('average_processing_days')}
            value={stats.averageProcessingDays}
            description="متوسط أيام المعالجة"
            icon={TrendingUp}
          />
        </div>

        {/* Main Content Grid */}
        <div className="grid gap-6 md:grid-cols-2">
          {/* Recent Requests */}
          <RecentRequests 
            requests={userRequests} 
            showActions={true}
          />

          {/* Quick Actions or Additional Info */}
          <Card>
            <CardHeader>
              <CardTitle>إجراءات سريعة</CardTitle>
              <CardDescription>
                الإجراءات الأكثر استخداماً
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {userRole === 'zakat_applicant' ? (
                <>
                  <div className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors cursor-pointer">
                    <div className="flex items-center gap-3">
                      <FileText className="h-5 w-5 text-primary" />
                      <div>
                        <p className="font-medium">طلب مساعدة جديد</p>
                        <p className="text-sm text-muted-foreground">تقديم طلب مساعدة جديد</p>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors cursor-pointer">
                    <div className="flex items-center gap-3">
                      <Users className="h-5 w-5 text-primary" />
                      <div>
                        <p className="font-medium">تحديث الملف الشخصي</p>
                        <p className="text-sm text-muted-foreground">تحديث البيانات الشخصية</p>
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                <>
                  <div className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors cursor-pointer">
                    <div className="flex items-center gap-3">
                      <Clock className="h-5 w-5 text-primary" />
                      <div>
                        <p className="font-medium">المهام المعلقة</p>
                        <p className="text-sm text-muted-foreground">{stats.pendingReview} مهمة في الانتظار</p>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors cursor-pointer">
                    <div className="flex items-center gap-3">
                      <FileText className="h-5 w-5 text-primary" />
                      <div>
                        <p className="font-medium">التقارير</p>
                        <p className="text-sm text-muted-foreground">عرض التقارير والإحصائيات</p>
                      </div>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}
