
'use client'

import { useSession } from 'next-auth/react'
import { useTranslation } from 'react-i18next'
import { useRouter } from 'next/navigation'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { FileText, Upload, Send, ArrowLeft } from 'lucide-react'
import { useState } from 'react'
import { mockAssistanceTypes } from '@/lib/mock-data'
import Link from 'next/link'
import { toast } from '@/hooks/use-toast'

export default function NewRequestPage() {
  const { data: session } = useSession() || {}
  const { t, i18n } = useTranslation()
  const router = useRouter()
  
  const [selectedType, setSelectedType] = useState('')
  const [formData, setFormData] = useState({
    assistanceTypeId: '',
    requestedAmount: '',
    description: '',
    urgencyLevel: 'medium',
    additionalInfo: '',
  })
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([])
  const [agreedToTerms, setAgreedToTerms] = useState(false)

  if (!session?.user) {
    return null
  }

  const selectedAssistanceType = mockAssistanceTypes.find(type => type.id === selectedType)

  const handleTypeChange = (typeId: string) => {
    setSelectedType(typeId)
    setFormData(prev => ({ ...prev, assistanceTypeId: typeId }))
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    setUploadedFiles(prev => [...prev, ...files])
  }

  const removeFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!selectedType) {
      toast({
        title: 'خطأ في النموذج',
        description: 'يرجى اختيار نوع المساعدة',
        variant: 'destructive',
      })
      return
    }

    if (!agreedToTerms) {
      toast({
        title: 'خطأ في النموذج',
        description: 'يرجى الموافقة على الشروط والأحكام',
        variant: 'destructive',
      })
      return
    }

    // Mock submission
    toast({
      title: 'تم إرسال الطلب بنجاح',
      description: 'سيتم مراجعة طلبك وإشعارك بالنتيجة قريباً',
    })

    router.push('/requests')
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(i18n.language === 'ar' ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: 'SAR',
      maximumFractionDigits: 0,
    }).format(amount)
  }

  return (
    <DashboardLayout>
      <div className="space-y-6 max-w-4xl">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/requests">
              <ArrowLeft className="h-4 w-4 mr-2" />
              العودة
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              تقديم طلب مساعدة جديد
            </h1>
            <p className="text-muted-foreground">
              املأ النموذج أدناه لتقديم طلب المساعدة
            </p>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Assistance Type Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                نوع المساعدة المطلوبة
              </CardTitle>
              <CardDescription>
                اختر نوع المساعدة التي تحتاجها
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4">
                {mockAssistanceTypes.map((type) => (
                  <div
                    key={type.id}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      selectedType === type.id
                        ? 'border-primary bg-primary/5'
                        : 'hover:bg-muted/50'
                    }`}
                    onClick={() => handleTypeChange(type.id)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="space-y-1">
                        <h3 className="font-medium">
                          {i18n.language === 'ar' ? type.nameAr : type.nameEn}
                        </h3>
                        <p className="text-sm text-muted-foreground">
                          {i18n.language === 'ar' ? type.descriptionAr : type.descriptionEn}
                        </p>
                        <p className="text-sm font-medium text-green-600">
                          الحد الأقصى: {formatCurrency(type.maxAmount)}
                        </p>
                      </div>
                      <input
                        type="radio"
                        name="assistanceType"
                        value={type.id}
                        checked={selectedType === type.id}
                        onChange={() => handleTypeChange(type.id)}
                        className="mt-1"
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Request Details */}
          {selectedType && (
            <Card>
              <CardHeader>
                <CardTitle>تفاصيل الطلب</CardTitle>
                <CardDescription>
                  أدخل تفاصيل طلب المساعدة
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="requestedAmount">المبلغ المطلوب (ريال سعودي)</Label>
                    <Input
                      id="requestedAmount"
                      type="number"
                      placeholder="0"
                      value={formData.requestedAmount}
                      onChange={(e) => setFormData(prev => ({ ...prev, requestedAmount: e.target.value }))}
                      max={selectedAssistanceType?.maxAmount}
                      required
                    />
                    {selectedAssistanceType && (
                      <p className="text-xs text-muted-foreground mt-1">
                        الحد الأقصى: {formatCurrency(selectedAssistanceType.maxAmount)}
                      </p>
                    )}
                  </div>
                  <div>
                    <Label htmlFor="urgencyLevel">مستوى الأولوية</Label>
                    <Select
                      value={formData.urgencyLevel}
                      onValueChange={(value) => setFormData(prev => ({ ...prev, urgencyLevel: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">منخفضة</SelectItem>
                        <SelectItem value="medium">متوسطة</SelectItem>
                        <SelectItem value="high">عالية</SelectItem>
                        <SelectItem value="urgent">عاجلة</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="description">وصف الحالة والحاجة للمساعدة</Label>
                  <Textarea
                    id="description"
                    placeholder="اشرح حالتك والسبب في طلب المساعدة بالتفصيل..."
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    rows={4}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="additionalInfo">معلومات إضافية (اختياري)</Label>
                  <Textarea
                    id="additionalInfo"
                    placeholder="أي معلومات إضافية قد تساعد في تقييم طلبك..."
                    value={formData.additionalInfo}
                    onChange={(e) => setFormData(prev => ({ ...prev, additionalInfo: e.target.value }))}
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {/* Required Documents */}
          {selectedAssistanceType && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Upload className="h-5 w-5" />
                  المستندات المطلوبة
                </CardTitle>
                <CardDescription>
                  يرجى رفع جميع المستندات المطلوبة لنوع المساعدة المحددة
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Required Documents List */}
                <div className="space-y-3">
                  <h4 className="font-medium">المستندات المطلوبة:</h4>
                  {selectedAssistanceType.requiredDocuments.map((doc) => (
                    <div key={doc.id} className="flex items-center gap-3 p-3 bg-muted rounded-lg">
                      <div className={`w-2 h-2 rounded-full ${doc.isRequired ? 'bg-red-500' : 'bg-gray-400'}`} />
                      <div className="flex-1">
                        <p className="font-medium">
                          {i18n.language === 'ar' ? doc.nameAr : doc.nameEn}
                          {doc.isRequired && <span className="text-red-500 mr-1">*</span>}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          الصيغ المقبولة: {doc.acceptedFormats.join(', ')} | 
                          الحد الأقصى: {(doc.maxSizeKB / 1024).toFixed(1)} MB
                        </p>
                      </div>
                    </div>
                  ))}
                </div>

                {/* File Upload */}
                <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
                  <Upload className="h-8 w-8 mx-auto mb-4 text-muted-foreground" />
                  <div className="space-y-2">
                    <p className="text-sm font-medium">اسحب الملفات هنا أو اضغط للاختيار</p>
                    <p className="text-xs text-muted-foreground">PDF, JPG, PNG حتى 5 MB</p>
                  </div>
                  <input
                    type="file"
                    multiple
                    accept=".pdf,.jpg,.jpeg,.png"
                    onChange={handleFileUpload}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                  />
                </div>

                {/* Uploaded Files */}
                {uploadedFiles.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="font-medium">الملفات المرفوعة:</h4>
                    {uploadedFiles.map((file, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <FileText className="h-4 w-4 text-green-600" />
                          <div>
                            <p className="text-sm font-medium">{file.name}</p>
                            <p className="text-xs text-muted-foreground">
                              {(file.size / 1024 / 1024).toFixed(1)} MB
                            </p>
                          </div>
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFile(index)}
                        >
                          حذف
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Terms and Conditions */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-start space-x-2 space-x-reverse">
                <Checkbox
                  id="terms"
                  checked={agreedToTerms}
                  onCheckedChange={(checked) => setAgreedToTerms(checked as boolean)}
                />
                <div className="space-y-1 leading-none">
                  <Label htmlFor="terms" className="text-sm">
                    أوافق على الشروط والأحكام وأتعهد بصحة البيانات المقدمة
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    أتعهد بأن جميع المعلومات والمستندات المقدمة صحيحة ودقيقة، وأتحمل المسؤولية الكاملة عن أي معلومات خاطئة.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-end gap-4">
            <Button type="button" variant="outline" asChild>
              <Link href="/requests">إلغاء</Link>
            </Button>
            <Button type="submit" disabled={!selectedType || !agreedToTerms}>
              <Send className="mr-2 h-4 w-4" />
              إرسال الطلب
            </Button>
          </div>
        </form>
      </div>
    </DashboardLayout>
  )
}
