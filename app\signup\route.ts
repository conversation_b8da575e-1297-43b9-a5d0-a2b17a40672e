
import { NextRequest, NextResponse } from 'next/server'
import bcrypt from 'bcryptjs'
import { mockUsers } from '@/lib/mock-data'
import type { User } from '@/lib/types'

export async function POST(req: NextRequest) {
  try {
    const { email, password, fullName, nationalId, phoneNumber } = await req.json()

    // Validate input
    if (!email || !password || !fullName || !nationalId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Check if user already exists
    const existingUser = mockUsers.find(user => 
      user.email === email || user.nationalId === nationalId
    )

    if (existingUser) {
      return NextResponse.json(
        { error: 'User already exists with this email or national ID' },
        { status: 400 }
      )
    }

    // Hash password (in real app)
    const hashedPassword = await bcrypt.hash(password, 12)

    // Create new user (in mock data - in real app this would go to database)
    const newUser: User = {
      id: `user-${Date.now()}`,
      nationalId,
      email,
      fullName,
      nationality: 'Saudi Arabia', // Default for demo
      dateOfBirth: new Date('1990-01-01'), // Default for demo
      phoneNumber: phoneNumber || '',
      address: '',
      accountStatus: 'pending_approval',
      role: 'zakat_applicant',
      createdAt: new Date(),
    }

    // In a real app, save to database
    mockUsers.push(newUser)

    return NextResponse.json(
      { 
        message: 'User created successfully',
        user: {
          id: newUser.id,
          email: newUser.email,
          fullName: newUser.fullName,
          role: newUser.role,
          accountStatus: newUser.accountStatus,
        }
      },
      { status: 201 }
    )

  } catch (error) {
    console.error('Signup error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
