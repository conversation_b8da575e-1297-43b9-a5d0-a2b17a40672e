# نظام إدارة الزكاة - Zakat Management System

A comprehensive web application for managing Zakat (Islamic charity) requests and distributions, built with modern web technologies.

## 🌟 Features

- **Multi-language Support**: Arabic (RTL) and English interface
- **User Authentication**: Secure login/signup system with NextAuth.js
- **Dashboard**: Comprehensive overview of Zakat requests and distributions
- **Request Management**: Submit, track, and manage Zakat requests
- **Reports & Analytics**: Generate detailed reports with charts and visualizations
- **Profile Management**: User profile and settings management
- **Task Management**: Administrative task tracking
- **Responsive Design**: Mobile-friendly interface with dark/light theme support

## 🛠️ Technologies Used

- **Frontend**: Next.js 14, React 18, TypeScript
- **Styling**: Tailwind CSS, Radix UI Components
- **Authentication**: NextAuth.js
- **Database**: PostgreSQL with Prisma ORM
- **Charts**: Chart.js, Recharts, Plotly.js
- **Forms**: React Hook Form with Zod validation
- **Internationalization**: i18next
- **State Management**: Zust<PERSON>, Jotai
- **UI Components**: Radix <PERSON>, Lucide React Icons

## 📋 Prerequisites

Before running this application, make sure you have the following installed:

- **Node.js** (version 18 or higher)
- **npm** or **yarn** package manager
- **PostgreSQL** database server

## 🚀 Installation & Setup

### 1. Clone the Repository

```bash
git clone <repository-url>
cd zakat_management_system/app
```

### 2. Install Dependencies

```bash
npm install --legacy-peer-deps
```

Note: We use `--legacy-peer-deps` due to ESLint version conflicts.

### 3. Environment Configuration

Create a `.env` file in the root directory with the following variables:

```env
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# Database
DATABASE_URL="postgresql://username:password@localhost:5432/zakat_management_db"
```

### 4. Database Setup

1. Create a PostgreSQL database named `zakat_management_db`
2. Update the `DATABASE_URL` in your `.env` file with your database credentials
3. Generate Prisma client:

```bash
npx prisma generate
```

4. Run database migrations (if available):

```bash
npx prisma migrate dev
```

### 5. Start the Development Server

```bash
npm run dev
```

The application will be available at [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
app/
├── app/                    # Next.js app directory
│   ├── api/               # API routes
│   ├── auth/              # Authentication pages
│   ├── dashboard/         # Dashboard pages
│   ├── profile/           # Profile management
│   ├── reports/           # Reports and analytics
│   ├── requests/          # Zakat request management
│   ├── signup/            # User registration
│   ├── tasks/             # Task management
│   └── test/              # Test pages
├── components/            # Reusable UI components
│   ├── dashboard/         # Dashboard-specific components
│   ├── layout/            # Layout components
│   └── ui/                # Base UI components
├── hooks/                 # Custom React hooks
├── lib/                   # Utility functions and configurations
├── prisma/                # Database schema and migrations
├── providers/             # React context providers
└── types/                 # TypeScript type definitions
```

## 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## 🌐 Usage

1. **Access the Application**: Navigate to http://localhost:3000
2. **Authentication**: The app redirects to `/auth/login` by default
3. **Sign Up**: Create a new account or log in with existing credentials
4. **Dashboard**: View overview of Zakat requests and statistics
5. **Submit Requests**: Navigate to requests section to submit new Zakat requests
6. **Manage Profile**: Update user information in the profile section
7. **View Reports**: Access detailed analytics and reports

## 🔒 Security Features

- Secure authentication with NextAuth.js
- Password hashing with bcryptjs
- JWT token management
- Environment variable protection
- CSRF protection

## 🌍 Internationalization

The application supports:
- Arabic (العربية) - RTL layout
- English - LTR layout

Language detection is automatic based on browser settings.

## 🎨 UI/UX Features

- **Responsive Design**: Works on desktop, tablet, and mobile
- **Dark/Light Theme**: Toggle between themes
- **RTL Support**: Full right-to-left layout for Arabic
- **Accessibility**: ARIA labels and keyboard navigation
- **Modern UI**: Clean, professional interface with smooth animations

## 🐛 Troubleshooting

### Common Issues

1. **Dependency Conflicts**: Use `npm install --legacy-peer-deps`
2. **Database Connection**: Ensure PostgreSQL is running and DATABASE_URL is correct
3. **Port Already in Use**: Change the port in next.config.js or kill the process using port 3000

### Development Tips

- Check browser console for client-side errors
- Monitor terminal output for server-side issues
- Ensure all environment variables are properly set

## 📝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🤝 Support

For support and questions, please contact the development team or create an issue in the repository.

---

**Built with ❤️ for the Muslim community**
