
'use client'

import { useSession } from 'next-auth/react'
import { redirect } from 'next/navigation'
import { Navbar } from './navbar'
import { Sidebar } from './sidebar'
import { Toaster } from '../ui/toaster'
import { ReactNode } from 'react'

interface DashboardLayoutProps {
  children: ReactNode
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const { data: session, status } = useSession() || {}

  if (status === 'loading') {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
          <p className="text-sm text-muted-foreground">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  if (!session?.user) {
    redirect('/auth/login')
  }

  // Check account status
  if (session.user.accountStatus !== 'active') {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center space-y-4">
          <h1 className="text-2xl font-bold">حسابك في انتظار الموافقة</h1>
          <p className="text-muted-foreground">
            يرجى انتظار موافقة الإدارة على حسابك
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-screen flex flex-col">
      <Navbar />
      <div className="flex flex-1 overflow-hidden">
        <Sidebar />
        <main className="flex-1 overflow-y-auto bg-muted/30 p-6">
          <div className="mx-auto max-w-6xl">
            {children}
          </div>
        </main>
      </div>
      <Toaster />
    </div>
  )
}
