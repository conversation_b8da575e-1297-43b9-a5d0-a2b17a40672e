
'use client'

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '../ui/card'
import { StatusBadge } from '../ui/status-badge'
import { useTranslation } from 'react-i18next'
import { format } from 'date-fns'
import { ar } from 'date-fns/locale'
import type { AssistanceRequest } from '@/lib/types'
import Link from 'next/link'
import { Button } from '../ui/button'
import { Eye } from 'lucide-react'

interface RecentRequestsProps {
  requests: AssistanceRequest[]
  showActions?: boolean
}

export function RecentRequests({ requests, showActions = true }: RecentRequestsProps) {
  const { t, i18n } = useTranslation()
  
  const formatDate = (date: Date) => {
    return format(date, 'dd/MM/yyyy', {
      locale: i18n.language === 'ar' ? ar : undefined
    })
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(i18n.language === 'ar' ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: 'SAR',
      maximumFractionDigits: 0,
    }).format(amount)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {t('requests')}
          <span className="text-sm font-normal text-muted-foreground">
            ({requests.length})
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {requests.length === 0 ? (
            <p className="text-center text-muted-foreground py-8">
              لا توجد طلبات حالياً
            </p>
          ) : (
            requests.map((request) => (
              <div
                key={request.id}
                className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
              >
                <div className="flex-1 space-y-1">
                  <div className="flex items-center gap-2">
                    <p className="font-medium">
                      {i18n.language === 'ar' 
                        ? request.assistanceType.nameAr 
                        : request.assistanceType.nameEn
                      }
                    </p>
                    <StatusBadge status={request.status} />
                  </div>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span>{formatDate(request.submissionDate)}</span>
                    <span>{formatCurrency(request.requestedAmount)}</span>
                  </div>
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {request.description}
                  </p>
                </div>
                {showActions && (
                  <div className="ml-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      asChild
                    >
                      <Link href={`/requests/${request.id}`}>
                        <Eye className="h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  )
}
