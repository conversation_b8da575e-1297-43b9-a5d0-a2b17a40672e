
'use client'

import { Badge } from './badge'
import { useTranslation } from 'react-i18next'
import type { RequestStatus } from '@/lib/types'
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  FileText, 
  Eye, 
  RefreshCw,
  AlertTriangle 
} from 'lucide-react'

interface StatusBadgeProps {
  status: RequestStatus
  showIcon?: boolean
}

const statusConfig = {
  draft: { variant: 'secondary' as const, icon: FileText },
  submitted: { variant: 'default' as const, icon: Clock },
  reception_review: { variant: 'default' as const, icon: Eye },
  researcher_review: { variant: 'default' as const, icon: Eye },
  banking_expert_review: { variant: 'default' as const, icon: Eye },
  department_head_review: { variant: 'default' as const, icon: Eye },
  admin_manager_review: { variant: 'default' as const, icon: Eye },
  minister_review: { variant: 'default' as const, icon: Eye },
  approved: { variant: 'default' as const, icon: CheckCircle },
  rejected: { variant: 'destructive' as const, icon: XCircle },
  needs_more_info: { variant: 'outline' as const, icon: AlertTriangle },
  returned: { variant: 'secondary' as const, icon: RefreshCw },
}

export function StatusBadge({ status, showIcon = true }: StatusBadgeProps) {
  const { t } = useTranslation()
  const config = statusConfig[status]
  const Icon = config.icon
  
  return (
    <Badge variant={config.variant} className="gap-1">
      {showIcon && <Icon className="h-3 w-3" />}
      {t(status)}
    </Badge>
  )
}
