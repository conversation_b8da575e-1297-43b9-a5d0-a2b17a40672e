
'use client'

import { useSession } from 'next-auth/react'
import { useTranslation } from 'react-i18next'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { StatusBadge } from '@/components/ui/status-badge'
import { User, Edit, Save, Plus, Trash2, Building } from 'lucide-react'
import { useState } from 'react'
import { toast } from '@/hooks/use-toast'

export default function ProfilePage() {
  const { data: session } = useSession() || {}
  const { t } = useTranslation()
  const [isEditing, setIsEditing] = useState(false)
  const [profileData, setProfileData] = useState({
    // Basic Info
    fullName: session?.user?.name || '',
    nationalId: session?.user?.nationalId || '',
    email: session?.user?.email || '',
    phoneNumber: '+966500000002',
    address: 'الرياض، المملكة العربية السعودية',
    
    // Personal Details
    maritalStatus: 'married',
    familyMembersCount: 4,
    
    // Employment
    employmentStatus: 'unemployed',
    monthlyIncome: 0,
    
    // Family Members
    familyMembers: [
      { name: 'فاطمة أحمد', relationship: 'spouse', age: 35, hasDisability: false, isDependent: true },
      { name: 'عبدالله أحمد', relationship: 'child', age: 12, hasDisability: false, isDependent: true },
      { name: 'نورا أحمد', relationship: 'child', age: 8, hasDisability: false, isDependent: true },
    ],
    
    // Assets & Liabilities
    assets: [
      { type: 'property', description: 'منزل سكن', estimatedValue: 500000 }
    ],
    liabilities: [
      { type: 'loan', description: 'قرض شخصي', amount: 25000, monthlyPayment: 1500 }
    ]
  })

  if (!session?.user) {
    return null
  }

  const handleSave = () => {
    // Mock save operation
    toast({
      title: 'تم حفظ البيانات',
      description: 'تم تحديث الملف الشخصي بنجاح',
    })
    setIsEditing(false)
  }

  const addFamilyMember = () => {
    setProfileData(prev => ({
      ...prev,
      familyMembers: [
        ...prev.familyMembers,
        { name: '', relationship: 'child', age: 0, hasDisability: false, isDependent: true }
      ]
    }))
  }

  const removeFamilyMember = (index: number) => {
    setProfileData(prev => ({
      ...prev,
      familyMembers: prev.familyMembers.filter((_, i) => i !== index)
    }))
  }

  const updateFamilyMember = (index: number, field: string, value: any) => {
    setProfileData(prev => ({
      ...prev,
      familyMembers: prev.familyMembers.map((member, i) => 
        i === index ? { ...member, [field]: value } : member
      )
    }))
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {t('personal_profile')}
            </h1>
            <p className="text-muted-foreground">
              إدارة وتحديث معلوماتك الشخصية
            </p>
          </div>
          <div className="flex items-center gap-2">
            <StatusBadge status="approved" />
            {!isEditing ? (
              <Button onClick={() => setIsEditing(true)}>
                <Edit className="mr-2 h-4 w-4" />
                تعديل
              </Button>
            ) : (
              <div className="flex gap-2">
                <Button onClick={handleSave}>
                  <Save className="mr-2 h-4 w-4" />
                  حفظ
                </Button>
                <Button variant="outline" onClick={() => setIsEditing(false)}>
                  إلغاء
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              المعلومات الأساسية
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>الاسم الكامل</Label>
                <Input
                  value={profileData.fullName}
                  onChange={(e) => setProfileData(prev => ({ ...prev, fullName: e.target.value }))}
                  disabled={!isEditing}
                />
              </div>
              <div>
                <Label>رقم الهوية الوطنية</Label>
                <Input
                  value={profileData.nationalId}
                  disabled
                />
              </div>
              <div>
                <Label>البريد الإلكتروني</Label>
                <Input
                  value={profileData.email}
                  disabled
                />
              </div>
              <div>
                <Label>رقم الهاتف</Label>
                <Input
                  value={profileData.phoneNumber}
                  onChange={(e) => setProfileData(prev => ({ ...prev, phoneNumber: e.target.value }))}
                  disabled={!isEditing}
                />
              </div>
            </div>
            <div>
              <Label>العنوان</Label>
              <Textarea
                value={profileData.address}
                onChange={(e) => setProfileData(prev => ({ ...prev, address: e.target.value }))}
                disabled={!isEditing}
                rows={2}
              />
            </div>
          </CardContent>
        </Card>

        {/* Personal Details */}
        <Card>
          <CardHeader>
            <CardTitle>البيانات الشخصية</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label>الحالة الاجتماعية</Label>
                <Select
                  value={profileData.maritalStatus}
                  onValueChange={(value) => setProfileData(prev => ({ ...prev, maritalStatus: value }))}
                  disabled={!isEditing}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="single">أعزب</SelectItem>
                    <SelectItem value="married">متزوج</SelectItem>
                    <SelectItem value="divorced">مطلق</SelectItem>
                    <SelectItem value="widowed">أرمل</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>عدد أفراد الأسرة</Label>
                <Input
                  type="number"
                  value={profileData.familyMembersCount}
                  onChange={(e) => setProfileData(prev => ({ ...prev, familyMembersCount: parseInt(e.target.value) }))}
                  disabled={!isEditing}
                />
              </div>
              <div>
                <Label>حالة العمل</Label>
                <Select
                  value={profileData.employmentStatus}
                  onValueChange={(value) => setProfileData(prev => ({ ...prev, employmentStatus: value }))}
                  disabled={!isEditing}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="employed">موظف</SelectItem>
                    <SelectItem value="unemployed">عاطل عن العمل</SelectItem>
                    <SelectItem value="retired">متقاعد</SelectItem>
                    <SelectItem value="student">طالب</SelectItem>
                    <SelectItem value="disabled">غير قادر على العمل</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div>
              <Label>الدخل الشهري (ريال سعودي)</Label>
              <Input
                type="number"
                value={profileData.monthlyIncome}
                onChange={(e) => setProfileData(prev => ({ ...prev, monthlyIncome: parseInt(e.target.value) }))}
                disabled={!isEditing}
              />
            </div>
          </CardContent>
        </Card>

        {/* Family Members */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              أفراد الأسرة
              {isEditing && (
                <Button size="sm" onClick={addFamilyMember}>
                  <Plus className="h-4 w-4 mr-1" />
                  إضافة فرد
                </Button>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {profileData.familyMembers.map((member, index) => (
              <div key={index} className="p-4 border rounded-lg space-y-3">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">فرد الأسرة {index + 1}</h4>
                  {isEditing && (
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => removeFamilyMember(index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
                  <div>
                    <Label>الاسم</Label>
                    <Input
                      value={member.name}
                      onChange={(e) => updateFamilyMember(index, 'name', e.target.value)}
                      disabled={!isEditing}
                    />
                  </div>
                  <div>
                    <Label>صلة القرابة</Label>
                    <Select
                      value={member.relationship}
                      onValueChange={(value) => updateFamilyMember(index, 'relationship', value)}
                      disabled={!isEditing}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="spouse">زوج/زوجة</SelectItem>
                        <SelectItem value="child">ابن/ابنة</SelectItem>
                        <SelectItem value="parent">والد/والدة</SelectItem>
                        <SelectItem value="sibling">أخ/أخت</SelectItem>
                        <SelectItem value="other">أخرى</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label>العمر</Label>
                    <Input
                      type="number"
                      value={member.age}
                      onChange={(e) => updateFamilyMember(index, 'age', parseInt(e.target.value))}
                      disabled={!isEditing}
                    />
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={`dependent-${index}`}
                      checked={member.isDependent}
                      onChange={(e) => updateFamilyMember(index, 'isDependent', e.target.checked)}
                      disabled={!isEditing}
                    />
                    <Label htmlFor={`dependent-${index}`}>معال</Label>
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Assets */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5" />
              الممتلكات والالتزامات
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <h4 className="font-medium mb-3">الممتلكات</h4>
              <div className="space-y-2">
                {profileData.assets.map((asset, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                    <div>
                      <p className="font-medium">{asset.description}</p>
                      <p className="text-sm text-muted-foreground">{asset.type}</p>
                    </div>
                    <p className="font-medium">
                      {new Intl.NumberFormat('ar-SA', {
                        style: 'currency',
                        currency: 'SAR'
                      }).format(asset.estimatedValue)}
                    </p>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-3">الالتزامات المالية</h4>
              <div className="space-y-2">
                {profileData.liabilities.map((liability, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                    <div>
                      <p className="font-medium">{liability.description}</p>
                      <p className="text-sm text-muted-foreground">دفعة شهرية: {liability.monthlyPayment} ريال</p>
                    </div>
                    <p className="font-medium text-red-600">
                      {new Intl.NumberFormat('ar-SA', {
                        style: 'currency',
                        currency: 'SAR'
                      }).format(liability.amount)}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
