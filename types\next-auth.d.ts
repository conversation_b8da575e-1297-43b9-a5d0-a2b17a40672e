
import { UserR<PERSON> } from '@/lib/types'
import <PERSON><PERSON><PERSON>, { DefaultSession } from 'next-auth'

declare module 'next-auth' {
  interface Session {
    user: {
      id: string
      role: UserRole
      nationalId: string
      accountStatus: string
    } & DefaultSession['user']
  }

  interface User {
    id: string
    role: UserRole
    nationalId: string
    accountStatus: string
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    role: UserRole
    nationalId: string
    accountStatus: string
  }
}
