
import { User, PersonalProfile, AssistanceRequest, AssistanceType, Task, DashboardStats, UserRole } from './types';

// Mock Users for each role
export const mockUsers: User[] = [
  // Test admin account
  {
    id: '1',
    nationalId: '**********',
    email: '<EMAIL>',
    fullName: '<PERSON>',
    nationality: 'Saudi Arabia',
    dateOfBirth: new Date('1980-01-01'),
    phoneNumber: '+************',
    address: 'Riyadh, Saudi Arabia',
    accountStatus: 'active',
    role: 'system_admin',
    createdAt: new Date('2023-01-01'),
    lastLogin: new Date(),
  },
  // Zakat Applicants
  {
    id: '2',
    nationalId: '**********',
    email: '<EMAIL>',
    fullName: '<PERSON>',
    nationality: 'Saudi Arabia',
    dateOfBirth: new Date('1985-05-15'),
    phoneNumber: '+************',
    address: 'Jeddah, Saudi Arabia',
    accountStatus: 'active',
    role: 'zakat_applicant',
    createdAt: new Date('2023-02-01'),
    lastLogin: new Date(),
    profileId: 'profile-2',
  },
  {
    id: '3',
    nationalId: '**********',
    email: '<EMAIL>',
    fullName: 'Fatima Omar Al-Zahra',
    nationality: 'Saudi Arabia',
    dateOfBirth: new Date('1990-08-20'),
    phoneNumber: '+************',
    address: 'Dammam, Saudi Arabia',
    accountStatus: 'pending_approval',
    role: 'zakat_applicant',
    createdAt: new Date('2023-03-01'),
    profileId: 'profile-3',
  },
  // Staff Members
  {
    id: '4',
    nationalId: '**********',
    email: '<EMAIL>',
    fullName: 'Sara Abdullah Al-Mansouri',
    nationality: 'Saudi Arabia',
    dateOfBirth: new Date('1988-03-10'),
    phoneNumber: '+************',
    address: 'Riyadh, Saudi Arabia',
    accountStatus: 'active',
    role: 'reception_staff',
    createdAt: new Date('2022-01-15'),
    lastLogin: new Date(),
  },
  {
    id: '5',
    nationalId: '**********',
    email: '<EMAIL>',
    fullName: 'Mohammed Hassan Al-Qadiri',
    nationality: 'Saudi Arabia',
    dateOfBirth: new Date('1982-07-25'),
    phoneNumber: '+************',
    address: 'Riyadh, Saudi Arabia',
    accountStatus: 'active',
    role: 'researcher',
    createdAt: new Date('2022-02-01'),
    lastLogin: new Date(),
  },
  {
    id: '6',
    nationalId: '**********',
    email: '<EMAIL>',
    fullName: 'Khalid Ahmed Al-Othman',
    nationality: 'Saudi Arabia',
    dateOfBirth: new Date('1975-12-05'),
    phoneNumber: '+************',
    address: 'Riyadh, Saudi Arabia',
    accountStatus: 'active',
    role: 'banking_expert',
    createdAt: new Date('2021-01-01'),
    lastLogin: new Date(),
  },
  {
    id: '7',
    nationalId: '**********',
    email: '<EMAIL>',
    fullName: 'Nasser Fahad Al-Saud',
    nationality: 'Saudi Arabia',
    dateOfBirth: new Date('1970-04-18'),
    phoneNumber: '+************',
    address: 'Riyadh, Saudi Arabia',
    accountStatus: 'active',
    role: 'department_head',
    createdAt: new Date('2020-01-01'),
    lastLogin: new Date(),
  },
  {
    id: '8',
    nationalId: '**********',
    email: '<EMAIL>',
    fullName: 'Abdulaziz Mohammed Al-Rashid',
    nationality: 'Saudi Arabia',
    dateOfBirth: new Date('1968-09-12'),
    phoneNumber: '+************',
    address: 'Riyadh, Saudi Arabia',
    accountStatus: 'active',
    role: 'admin_manager',
    createdAt: new Date('2019-01-01'),
    lastLogin: new Date(),
  },
  {
    id: '9',
    nationalId: '**********',
    email: '<EMAIL>',
    fullName: 'His Excellency Abdullah bin Salman',
    nationality: 'Saudi Arabia',
    dateOfBirth: new Date('1965-02-28'),
    phoneNumber: '+************',
    address: 'Riyadh, Saudi Arabia',
    accountStatus: 'active',
    role: 'minister',
    createdAt: new Date('2018-01-01'),
    lastLogin: new Date(),
  },
];

// Mock Assistance Types
export const mockAssistanceTypes: AssistanceType[] = [
  {
    id: 'financial-support',
    nameAr: 'المساعدة المالية العامة',
    nameEn: 'General Financial Support',
    descriptionAr: 'مساعدة مالية للأسر المحتاجة',
    descriptionEn: 'Financial assistance for needy families',
    maxAmount: 25000,
    requiredDocuments: [
      {
        id: 'salary-certificate',
        nameAr: 'شهادة راتب',
        nameEn: 'Salary Certificate',
        isRequired: true,
        acceptedFormats: ['pdf', 'jpg', 'png'],
        maxSizeKB: 2048,
      },
      {
        id: 'bank-statement',
        nameAr: 'كشف حساب بنكي',
        nameEn: 'Bank Statement',
        isRequired: true,
        acceptedFormats: ['pdf'],
        maxSizeKB: 5120,
      },
    ],
    eligibilityCriteria: [
      {
        field: 'monthlyIncome',
        condition: 'less_than',
        value: 5000,
        nationality: 'Saudi Arabia',
      },
    ],
    isActive: true,
    category: 'Financial',
  },
  {
    id: 'medical-support',
    nameAr: 'المساعدة الطبية',
    nameEn: 'Medical Support',
    descriptionAr: 'مساعدة لتغطية التكاليف الطبية',
    descriptionEn: 'Assistance to cover medical expenses',
    maxAmount: 50000,
    requiredDocuments: [
      {
        id: 'medical-report',
        nameAr: 'التقرير الطبي',
        nameEn: 'Medical Report',
        isRequired: true,
        acceptedFormats: ['pdf', 'jpg', 'png'],
        maxSizeKB: 3072,
      },
      {
        id: 'medical-bills',
        nameAr: 'الفواتير الطبية',
        nameEn: 'Medical Bills',
        isRequired: true,
        acceptedFormats: ['pdf', 'jpg', 'png'],
        maxSizeKB: 5120,
      },
    ],
    eligibilityCriteria: [],
    isActive: true,
    category: 'Medical',
  },
  {
    id: 'education-support',
    nameAr: 'المساعدة التعليمية',
    nameEn: 'Education Support',
    descriptionAr: 'مساعدة لتغطية تكاليف التعليم',
    descriptionEn: 'Assistance to cover education expenses',
    maxAmount: 15000,
    requiredDocuments: [
      {
        id: 'enrollment-certificate',
        nameAr: 'شهادة قيد',
        nameEn: 'Enrollment Certificate',
        isRequired: true,
        acceptedFormats: ['pdf', 'jpg', 'png'],
        maxSizeKB: 2048,
      },
    ],
    eligibilityCriteria: [
      {
        field: 'familyMembersCount',
        condition: 'greater_than',
        value: 0,
      },
    ],
    isActive: true,
    category: 'Education',
  },
];

// Mock Assistance Requests
export const mockAssistanceRequests: AssistanceRequest[] = [
  {
    id: 'req-001',
    userId: '2',
    assistanceType: mockAssistanceTypes[0],
    requestedAmount: 15000,
    approvedAmount: 12000,
    description: 'نحتاج إلى مساعدة مالية لتغطية تكاليف المعيشة بعد توقف العمل',
    status: 'approved',
    submissionDate: new Date('2023-10-15'),
    lastUpdateDate: new Date('2023-11-01'),
    attachedDocuments: [],
    workflow: [
      {
        id: 'step-1',
        requestId: 'req-001',
        stage: 'reception_review',
        reviewerId: '4',
        reviewerName: 'Sara Abdullah Al-Mansouri',
        stageStartDate: new Date('2023-10-15'),
        stageEndDate: new Date('2023-10-16'),
        decision: 'approve',
        decisionDate: new Date('2023-10-16'),
        notes: 'المستندات كاملة والحالة تستدعي المساعدة',
      },
      {
        id: 'step-2',
        requestId: 'req-001',
        stage: 'approved',
        reviewerId: '8',
        reviewerName: 'Abdulaziz Mohammed Al-Rashid',
        stageStartDate: new Date('2023-10-30'),
        stageEndDate: new Date('2023-11-01'),
        decision: 'approve',
        decisionDate: new Date('2023-11-01'),
        notes: 'تمت الموافقة على مبلغ 12,000 ريال',
      },
    ],
    priority: 'medium',
  },
  {
    id: 'req-002',
    userId: '3',
    assistanceType: mockAssistanceTypes[1],
    requestedAmount: 35000,
    description: 'نحتاج مساعدة لتغطية تكاليف علاج والدي في المستشفى',
    status: 'researcher_review',
    submissionDate: new Date('2023-11-10'),
    lastUpdateDate: new Date('2023-11-12'),
    attachedDocuments: [],
    workflow: [
      {
        id: 'step-1',
        requestId: 'req-002',
        stage: 'reception_review',
        reviewerId: '4',
        reviewerName: 'Sara Abdullah Al-Mansouri',
        stageStartDate: new Date('2023-11-10'),
        stageEndDate: new Date('2023-11-11'),
        decision: 'approve',
        decisionDate: new Date('2023-11-11'),
        notes: 'تم مراجعة الطلب وإحالته للباحث',
      },
      {
        id: 'step-2',
        requestId: 'req-002',
        stage: 'researcher_review',
        reviewerId: '5',
        reviewerName: 'Mohammed Hassan Al-Qadiri',
        stageStartDate: new Date('2023-11-11'),
        notes: 'قيد المراجعة والتحقق من المستندات الطبية',
      },
    ],
    priority: 'high',
  },
];

// Mock Tasks for different roles
export const mockTasks: Record<UserRole, Task[]> = {
  reception_staff: [
    {
      id: 'task-1',
      assignedTo: '4',
      requestId: 'req-003',
      type: 'profile_review',
      priority: 'medium',
      dueDate: new Date(Date.now() + ********), // Tomorrow
      status: 'pending',
      createdDate: new Date(),
    },
    {
      id: 'task-2',
      assignedTo: '4',
      requestId: 'req-004',
      type: 'request_review',
      priority: 'high',
      status: 'in_progress',
      createdDate: new Date(Date.now() - ********), // Yesterday
    },
  ],
  researcher: [
    {
      id: 'task-3',
      assignedTo: '5',
      requestId: 'req-002',
      type: 'request_review',
      priority: 'high',
      dueDate: new Date(Date.now() + *********), // Day after tomorrow
      status: 'in_progress',
      createdDate: new Date('2023-11-11'),
    },
  ],
  banking_expert: [
    {
      id: 'task-4',
      assignedTo: '6',
      requestId: 'req-005',
      type: 'request_review',
      priority: 'medium',
      dueDate: new Date(Date.now() + *********), // 3 days from now
      status: 'pending',
      createdDate: new Date(),
    },
  ],
  department_head: [],
  admin_manager: [],
  minister: [],
  zakat_applicant: [],
  system_admin: [],
};

// Mock Dashboard Stats for different roles
export const mockDashboardStats: Record<UserRole, DashboardStats> = {
  reception_staff: {
    totalRequests: 45,
    pendingReview: 8,
    approvedToday: 3,
    rejectedToday: 1,
    averageProcessingDays: 2,
    totalUsers: 150,
  },
  researcher: {
    totalRequests: 32,
    pendingReview: 5,
    approvedToday: 2,
    rejectedToday: 0,
    averageProcessingDays: 3,
    totalUsers: 150,
  },
  banking_expert: {
    totalRequests: 28,
    pendingReview: 4,
    approvedToday: 1,
    rejectedToday: 1,
    averageProcessingDays: 4,
    totalUsers: 150,
  },
  department_head: {
    totalRequests: 15,
    pendingReview: 2,
    approvedToday: 1,
    rejectedToday: 0,
    averageProcessingDays: 5,
    totalUsers: 150,
  },
  admin_manager: {
    totalRequests: 120,
    pendingReview: 8,
    approvedToday: 5,
    rejectedToday: 2,
    averageProcessingDays: 12,
    totalUsers: 150,
  },
  minister: {
    totalRequests: 8,
    pendingReview: 1,
    approvedToday: 0,
    rejectedToday: 0,
    averageProcessingDays: 7,
    totalUsers: 150,
  },
  zakat_applicant: {
    totalRequests: 3,
    pendingReview: 1,
    approvedToday: 0,
    rejectedToday: 0,
    averageProcessingDays: 14,
    totalUsers: 1,
  },
  system_admin: {
    totalRequests: 200,
    pendingReview: 25,
    approvedToday: 12,
    rejectedToday: 3,
    averageProcessingDays: 10,
    totalUsers: 150,
  },
};

// Helper function to get user by ID
export const getUserById = (id: string): User | undefined => {
  return mockUsers.find(user => user.id === id);
};

// Helper function to get user by email
export const getUserByEmail = (email: string): User | undefined => {
  return mockUsers.find(user => user.email === email);
};

// Helper function to get requests by user ID
export const getRequestsByUserId = (userId: string): AssistanceRequest[] => {
  return mockAssistanceRequests.filter(request => request.userId === userId);
};

// Helper function to get tasks by user ID
export const getTasksByUserId = (userId: string): Task[] => {
  const user = getUserById(userId);
  if (!user) return [];
  return mockTasks[user.role] || [];
};
