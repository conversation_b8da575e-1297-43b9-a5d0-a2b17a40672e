
'use client'

import { useSession } from 'next-auth/react'
import { useTranslation } from 'react-i18next'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  BarChart3, 
  Download, 
  FileText, 
  TrendingUp, 
  Users, 
  CreditCard,
  Calendar,
  PieChart
} from 'lucide-react'
import { mockDashboardStats, mockAssistanceRequests } from '@/lib/mock-data'
import { format } from 'date-fns'
import { ar } from 'date-fns/locale'

export default function ReportsPage() {
  const { data: session } = useSession() || {}
  const { t, i18n } = useTranslation()

  if (!session?.user) {
    return null
  }

  // Only show for authorized roles
  const authorizedRoles = ['reception_staff', 'researcher', 'banking_expert', 'department_head', 'admin_manager', 'minister', 'system_admin']
  if (!authorizedRoles.includes(session.user.role)) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-2">غير مصرح</h2>
            <p className="text-muted-foreground">ليس لديك صلاحية للوصول إلى التقارير</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  const stats = mockDashboardStats[session.user.role]
  
  // Mock report data
  const monthlyData = [
    { month: 'يناير', requests: 45, approved: 38, rejected: 7, amount: 450000 },
    { month: 'فبراير', requests: 52, approved: 44, rejected: 8, amount: 520000 },
    { month: 'مارس', requests: 48, approved: 41, rejected: 7, amount: 480000 },
    { month: 'أبريل', requests: 63, approved: 55, rejected: 8, amount: 630000 },
    { month: 'مايو', requests: 57, approved: 49, rejected: 8, amount: 570000 },
    { month: 'يونيو', requests: 61, approved: 52, rejected: 9, amount: 610000 },
  ]

  const categoryData = [
    { category: 'المساعدة المالية العامة', count: 180, amount: 2400000, percentage: 45 },
    { category: 'المساعدة الطبية', count: 120, amount: 3600000, percentage: 30 },
    { category: 'المساعدة التعليمية', count: 100, amount: 800000, percentage: 25 },
  ]

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(i18n.language === 'ar' ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: 'SAR',
      maximumFractionDigits: 0,
    }).format(amount)
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {t('reports')}
            </h1>
            <p className="text-muted-foreground">
              تقارير وإحصائيات شاملة عن النظام
            </p>
          </div>
          <Button>
            <Download className="mr-2 h-4 w-4" />
            تصدير التقارير
          </Button>
        </div>

        {/* Summary Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                إجمالي الطلبات
              </CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">400</div>
              <p className="text-xs text-muted-foreground">
                +12% من الشهر الماضي
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                المبلغ المعتمد
              </CardTitle>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(4860000)}</div>
              <p className="text-xs text-muted-foreground">
                +8% من الشهر الماضي
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                معدل الموافقة
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">85%</div>
              <p className="text-xs text-muted-foreground">
                +2% من الشهر الماضي
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                متوسط المعالجة
              </CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">12 يوم</div>
              <p className="text-xs text-muted-foreground">
                -1 يوم من الشهر الماضي
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Reports Tabs */}
        <Tabs defaultValue="monthly" className="space-y-4">
          <TabsList>
            <TabsTrigger value="monthly">التقرير الشهري</TabsTrigger>
            <TabsTrigger value="categories">تقرير الفئات</TabsTrigger>
            <TabsTrigger value="performance">تقرير الأداء</TabsTrigger>
          </TabsList>

          {/* Monthly Report */}
          <TabsContent value="monthly" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  التقرير الشهري
                </CardTitle>
                <CardDescription>
                  إحصائيات الطلبات والموافقات الشهرية
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {monthlyData.map((month, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="space-y-1">
                        <p className="font-medium">{month.month}</p>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <span>الطلبات: {month.requests}</span>
                          <span className="text-green-600">موافق: {month.approved}</span>
                          <span className="text-red-600">مرفوض: {month.rejected}</span>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-bold">{formatCurrency(month.amount)}</p>
                        <p className="text-sm text-muted-foreground">إجمالي المعتمد</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Categories Report */}
          <TabsContent value="categories" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5" />
                  تقرير الفئات
                </CardTitle>
                <CardDescription>
                  توزيع الطلبات حسب نوع المساعدة
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {categoryData.map((category, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">{category.category}</h4>
                        <Badge variant="secondary">{category.percentage}%</Badge>
                      </div>
                      <div className="flex items-center justify-between text-sm text-muted-foreground">
                        <span>{category.count} طلب</span>
                        <span>{formatCurrency(category.amount)}</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div 
                          className="bg-primary h-2 rounded-full transition-all duration-300"
                          style={{ width: `${category.percentage}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Performance Report */}
          <TabsContent value="performance" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  تقرير الأداء
                </CardTitle>
                <CardDescription>
                  مؤشرات الأداء الرئيسية للنظام
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-6 md:grid-cols-2">
                  {/* Response Time */}
                  <div className="space-y-3">
                    <h4 className="font-medium">أوقات الاستجابة</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>مراجعة الاستقبال</span>
                        <span>2 أيام</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>مراجعة الباحث</span>
                        <span>3 أيام</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>الموافقة النهائية</span>
                        <span>7 أيام</span>
                      </div>
                    </div>
                  </div>

                  {/* Staff Performance */}
                  <div className="space-y-3">
                    <h4 className="font-medium">أداء الموظفين</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>الطلبات المكتملة</span>
                        <span className="text-green-600">340</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>متوسط وقت المراجعة</span>
                        <span>4.5 ساعات</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>معدل الدقة</span>
                        <span className="text-green-600">98%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>النشاط الأخير</CardTitle>
            <CardDescription>آخر العمليات في النظام</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockAssistanceRequests.slice(0, 5).map((request) => (
                <div key={request.id} className="flex items-center justify-between p-3 border-b last:border-b-0">
                  <div className="flex items-center gap-3">
                    <FileText className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">
                        طلب {request.id} - {i18n.language === 'ar' ? request.assistanceType.nameAr : request.assistanceType.nameEn}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {format(request.lastUpdateDate, 'dd/MM/yyyy HH:mm')}
                      </p>
                    </div>
                  </div>
                  <Badge 
                    className={
                      request.status === 'approved' ? 'bg-green-100 text-green-800' :
                      request.status === 'rejected' ? 'bg-red-100 text-red-800' :
                      'bg-yellow-100 text-yellow-800'
                    }
                  >
                    {request.status === 'approved' ? 'موافق عليه' :
                     request.status === 'rejected' ? 'مرفوض' : 'قيد المراجعة'}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
