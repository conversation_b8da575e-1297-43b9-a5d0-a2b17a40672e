
'use client'

import { useSession } from 'next-auth/react'
import { useTranslation } from 'react-i18next'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Input } from '@/components/ui/input'
import { 
  ClipboardList, 
  Search, 
  Filter, 
  Eye, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  Calendar,
  User,
  FileText
} from 'lucide-react'
import { mockTasks, getTasksByUserId, mockAssistanceRequests } from '@/lib/mock-data'
import { format } from 'date-fns'
import { ar } from 'date-fns/locale'
import Link from 'next/link'
import { useState } from 'react'
import type { UserRole } from '@/lib/types'

export default function TasksPage() {
  const { data: session } = useSession() || {}
  const { t, i18n } = useTranslation()
  const [searchTerm, setSearchTerm] = useState('')
  const [activeTab, setActiveTab] = useState('pending')

  if (!session?.user) {
    return null
  }

  // Only show for staff roles
  const staffRoles: UserRole[] = ['reception_staff', 'researcher', 'banking_expert', 'department_head', 'admin_manager', 'minister']
  if (!staffRoles.includes(session.user.role)) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-2">غير مصرح</h2>
            <p className="text-muted-foreground">ليس لديك صلاحية للوصول إلى هذه الصفحة</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  const userTasks = getTasksByUserId(session.user.id)
  
  // Mock additional tasks for demonstration
  const allTasks = [
    ...userTasks,
    {
      id: 'task-demo-1',
      assignedTo: session.user.id,
      requestId: 'req-001',
      type: 'request_review' as const,
      priority: 'high' as const,
      dueDate: new Date(Date.now() + ********),
      status: 'pending' as const,
      createdDate: new Date(Date.now() - 3600000),
    },
    {
      id: 'task-demo-2',
      assignedTo: session.user.id,
      requestId: 'req-002',
      type: 'document_verification' as const,
      priority: 'medium' as const,
      status: 'in_progress' as const,
      createdDate: new Date(Date.now() - 7200000),
    },
    {
      id: 'task-demo-3',
      assignedTo: session.user.id,
      requestId: 'req-003',
      type: 'profile_review' as const,
      priority: 'low' as const,
      status: 'completed' as const,
      createdDate: new Date(Date.now() - ********),
      completedDate: new Date(Date.now() - 3600000),
    }
  ]

  const filteredTasks = allTasks.filter(task => {
    const matchesSearch = searchTerm === '' || task.id.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesTab = activeTab === 'all' || task.status === activeTab
    return matchesSearch && matchesTab
  })

  const formatDate = (date: Date) => {
    return format(date, 'dd/MM/yyyy HH:mm', {
      locale: i18n.language === 'ar' ? ar : undefined
    })
  }

  const getTaskTypeLabel = (type: string) => {
    const labels = {
      'profile_review': 'مراجعة الملف الشخصي',
      'request_review': 'مراجعة طلب المساعدة',
      'document_verification': 'التحقق من المستندات'
    }
    return labels[type as keyof typeof labels] || type
  }

  const getPriorityColor = (priority: string) => {
    const colors = {
      low: 'bg-gray-100 text-gray-800',
      medium: 'bg-blue-100 text-blue-800',
      high: 'bg-orange-100 text-orange-800',
      urgent: 'bg-red-100 text-red-800'
    }
    return colors[priority as keyof typeof colors] || colors.medium
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />
      case 'in_progress':
        return <AlertTriangle className="h-4 w-4" />
      case 'completed':
        return <CheckCircle className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const taskCounts = {
    all: allTasks.length,
    pending: allTasks.filter(t => t.status === 'pending').length,
    in_progress: allTasks.filter(t => t.status === 'in_progress').length,
    completed: allTasks.filter(t => t.status === 'completed').length,
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {t('tasks')}
            </h1>
            <p className="text-muted-foreground">
              إدارة المهام المخصصة لك ومتابعة التقدم
            </p>
          </div>
        </div>

        {/* Search and Filter */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col gap-4 md:flex-row md:items-center">
              <div className="relative flex-1">
                <Search className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="البحث في المهام..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
              <Button variant="outline" size="sm">
                <Filter className="mr-2 h-4 w-4" />
                تصفية
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Tasks Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="all" className="flex items-center gap-2">
              الكل ({taskCounts.all})
            </TabsTrigger>
            <TabsTrigger value="pending" className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              معلقة ({taskCounts.pending})
            </TabsTrigger>
            <TabsTrigger value="in_progress" className="flex items-center gap-2">
              <AlertTriangle className="h-4 w-4" />
              جارية ({taskCounts.in_progress})
            </TabsTrigger>
            <TabsTrigger value="completed" className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              مكتملة ({taskCounts.completed})
            </TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="space-y-4">
            {filteredTasks.length === 0 ? (
              <Card>
                <CardContent className="flex items-center justify-center py-16">
                  <div className="text-center space-y-4">
                    <div className="h-16 w-16 mx-auto bg-muted rounded-full flex items-center justify-center">
                      <ClipboardList className="h-8 w-8 text-muted-foreground" />
                    </div>
                    <div>
                      <p className="text-lg font-medium">لا توجد مهام</p>
                      <p className="text-muted-foreground">
                        {searchTerm 
                          ? 'لم يتم العثور على مهام تطابق البحث'
                          : 'لا توجد مهام في هذه الفئة'
                        }
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {filteredTasks.map((task) => {
                  // Find related request for display
                  const relatedRequest = mockAssistanceRequests.find(req => req.id === task.requestId)
                  
                  return (
                    <Card key={task.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between">
                          <div className="flex-1 space-y-3">
                            {/* Header */}
                            <div className="flex items-center gap-3">
                              {getStatusIcon(task.status)}
                              <h3 className="text-lg font-semibold">
                                {getTaskTypeLabel(task.type)}
                              </h3>
                              <Badge className={getPriorityColor(task.priority)}>
                                {task.priority === 'low' && 'منخفضة'}
                                {task.priority === 'medium' && 'متوسطة'}
                                {task.priority === 'high' && 'عالية'}
                                {task.priority === 'urgent' && 'عاجلة'}
                              </Badge>
                            </div>

                            {/* Details */}
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                              <div>
                                <p className="text-muted-foreground">رقم المهمة</p>
                                <p className="font-medium">{task.id}</p>
                              </div>
                              <div>
                                <p className="text-muted-foreground">تاريخ الإنشاء</p>
                                <p className="font-medium">{formatDate(task.createdDate)}</p>
                              </div>
                              {task.dueDate && (
                                <div>
                                  <p className="text-muted-foreground">موعد الاستحقاق</p>
                                  <p className={`font-medium ${
                                    new Date(task.dueDate) < new Date() ? 'text-red-600' : 'text-green-600'
                                  }`}>
                                    {formatDate(task.dueDate)}
                                  </p>
                                </div>
                              )}
                              {task.completedDate && (
                                <div>
                                  <p className="text-muted-foreground">تاريخ الإنجاز</p>
                                  <p className="font-medium">{formatDate(task.completedDate)}</p>
                                </div>
                              )}
                            </div>

                            {/* Related Request Info */}
                            {relatedRequest && (
                              <div className="p-3 bg-muted/50 rounded-lg">
                                <p className="text-sm font-medium mb-1">الطلب المرتبط:</p>
                                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                  <span>
                                    {i18n.language === 'ar' 
                                      ? relatedRequest.assistanceType.nameAr 
                                      : relatedRequest.assistanceType.nameEn
                                    }
                                  </span>
                                  <span>
                                    {new Intl.NumberFormat('ar-SA', {
                                      style: 'currency',
                                      currency: 'SAR'
                                    }).format(relatedRequest.requestedAmount)}
                                  </span>
                                </div>
                              </div>
                            )}
                          </div>

                          {/* Actions */}
                          <div className="flex items-center gap-2 ml-4">
                            <Button variant="outline" size="sm" asChild>
                              <Link href={`/requests/${task.requestId}`}>
                                <Eye className="h-4 w-4" />
                              </Link>
                            </Button>
                            {task.status !== 'completed' && (
                              <Button size="sm">
                                {task.status === 'pending' ? 'بدء العمل' : 'متابعة'}
                              </Button>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
