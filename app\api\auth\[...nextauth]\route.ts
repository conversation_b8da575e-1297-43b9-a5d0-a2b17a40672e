
import NextAuth from 'next-auth'
import Cred<PERSON><PERSON><PERSON>rovider from 'next-auth/providers/credentials'
import bcrypt from 'bcryptjs'
import { getUserByEmail } from '@/lib/mock-data'
import type { UserRole } from '@/lib/types'

const authOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        // Find user in mock data
        const user = getUserByEmail(credentials.email)
        if (!user) {
          return null
        }

        // For demo purposes, we'll use simple password check
        // In production, this should be properly hashed and compared
        const isValidPassword = credentials.password === 'johndoe123' || 
                               credentials.email === '<EMAIL>'

        if (!isValidPassword) {
          return null
        }

        return {
          id: user.id,
          email: user.email,
          name: user.fullName,
          role: user.role as UserRole,
          nationalId: user.nationalId,
          accountStatus: user.accountStatus,
        }
      }
    })
  ],
  session: {
    strategy: 'jwt' as const,
  },
  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async jwt({ token, user }: any) {
      if (user) {
        token.role = user.role
        token.nationalId = user.nationalId
        token.accountStatus = user.accountStatus
      }
      return token
    },
    async session({ session, token }: any) {
      if (token) {
        session.user.id = token.sub
        session.user.role = token.role
        session.user.nationalId = token.nationalId
        session.user.accountStatus = token.accountStatus
      }
      return session
    },
  },
  pages: {
    signIn: '/auth/login',
    error: '/auth/error',
  },
}

const handler = NextAuth(authOptions)

export { handler as GET, handler as POST }
