
'use client'

import { useSession } from 'next-auth/react'
import { useTranslation } from 'react-i18next'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { StatusBadge } from '@/components/ui/status-badge'
import { Plus, Search, Filter, Eye, Download } from 'lucide-react'
import { mockAssistanceRequests, getRequestsByUserId } from '@/lib/mock-data'
import { format } from 'date-fns'
import { ar } from 'date-fns/locale'
import Link from 'next/link'
import { useState } from 'react'

export default function RequestsPage() {
  const { data: session } = useSession() || {}
  const { t, i18n } = useTranslation()
  const [searchTerm, setSearchTerm] = useState('')

  if (!session?.user) {
    return null
  }

  const isApplicant = session.user.role === 'zakat_applicant'
  const allRequests = isApplicant 
    ? getRequestsByUserId(session.user.id)
    : mockAssistanceRequests

  // Filter requests based on search term
  const filteredRequests = allRequests.filter(request => {
    const searchLower = searchTerm.toLowerCase()
    const nameMatch = (i18n.language === 'ar' 
      ? request.assistanceType.nameAr 
      : request.assistanceType.nameEn
    ).toLowerCase().includes(searchLower)
    const descriptionMatch = request.description.toLowerCase().includes(searchLower)
    const idMatch = request.id.toLowerCase().includes(searchLower)
    
    return nameMatch || descriptionMatch || idMatch
  })

  const formatDate = (date: Date) => {
    return format(date, 'dd/MM/yyyy', {
      locale: i18n.language === 'ar' ? ar : undefined
    })
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(i18n.language === 'ar' ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: 'SAR',
      maximumFractionDigits: 0,
    }).format(amount)
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {t('requests')}
            </h1>
            <p className="text-muted-foreground">
              {isApplicant 
                ? 'إدارة طلبات المساعدة الخاصة بك'
                : 'مراجعة جميع طلبات المساعدة'
              }
            </p>
          </div>
          {isApplicant && (
            <Button asChild>
              <Link href="/requests/new">
                <Plus className="mr-2 h-4 w-4" />
                {t('new_request')}
              </Link>
            </Button>
          )}
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col gap-4 md:flex-row md:items-center">
              <div className="relative flex-1">
                <Search className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="البحث في الطلبات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
              <Button variant="outline" size="sm">
                <Filter className="mr-2 h-4 w-4" />
                تصفية
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Requests List */}
        <div className="space-y-4">
          {filteredRequests.length === 0 ? (
            <Card>
              <CardContent className="flex items-center justify-center py-16">
                <div className="text-center space-y-4">
                  <div className="h-16 w-16 mx-auto bg-muted rounded-full flex items-center justify-center">
                    <Search className="h-8 w-8 text-muted-foreground" />
                  </div>
                  <div>
                    <p className="text-lg font-medium">لا توجد طلبات</p>
                    <p className="text-muted-foreground">
                      {searchTerm 
                        ? 'لم يتم العثور على طلبات تطابق البحث'
                        : isApplicant 
                          ? 'لم تقم بتقديم أي طلبات بعد'
                          : 'لا توجد طلبات للمراجعة'
                      }
                    </p>
                  </div>
                  {isApplicant && !searchTerm && (
                    <Button asChild>
                      <Link href="/requests/new">
                        <Plus className="mr-2 h-4 w-4" />
                        تقديم طلب جديد
                      </Link>
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ) : (
            filteredRequests.map((request) => (
              <Card key={request.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 space-y-3">
                      {/* Header */}
                      <div className="flex items-center gap-3">
                        <h3 className="text-lg font-semibold">
                          {i18n.language === 'ar' 
                            ? request.assistanceType.nameAr 
                            : request.assistanceType.nameEn
                          }
                        </h3>
                        <StatusBadge status={request.status} />
                      </div>

                      {/* Details */}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <p className="text-muted-foreground">رقم الطلب</p>
                          <p className="font-medium">{request.id}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">تاريخ التقديم</p>
                          <p className="font-medium">{formatDate(request.submissionDate)}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">المبلغ المطلوب</p>
                          <p className="font-medium">{formatCurrency(request.requestedAmount)}</p>
                        </div>
                        {request.approvedAmount && (
                          <div>
                            <p className="text-muted-foreground">المبلغ المعتمد</p>
                            <p className="font-medium text-green-600">
                              {formatCurrency(request.approvedAmount)}
                            </p>
                          </div>
                        )}
                      </div>

                      {/* Description */}
                      <div>
                        <p className="text-muted-foreground text-sm mb-1">الوصف</p>
                        <p className="text-sm line-clamp-2">{request.description}</p>
                      </div>

                      {/* Workflow Progress */}
                      <div>
                        <p className="text-muted-foreground text-sm mb-2">مراحل المعالجة</p>
                        <div className="flex items-center gap-2 text-xs">
                          {request.workflow.map((step, index) => (
                            <div key={step.id} className="flex items-center">
                              <div className={`px-2 py-1 rounded ${
                                step.decision === 'approve' 
                                  ? 'bg-green-100 text-green-800' 
                                  : step.decision === 'reject' 
                                    ? 'bg-red-100 text-red-800'
                                    : 'bg-yellow-100 text-yellow-800'
                              }`}>
                                {step.reviewerName}
                              </div>
                              {index < request.workflow.length - 1 && (
                                <div className="w-2 h-px bg-muted-foreground mx-1" />
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center gap-2 ml-4">
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/requests/${request.id}`}>
                          <Eye className="h-4 w-4" />
                        </Link>
                      </Button>
                      {request.status === 'approved' && (
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
    </DashboardLayout>
  )
}
